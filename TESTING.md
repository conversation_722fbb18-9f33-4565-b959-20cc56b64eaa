# DrawX Extension Testing Guide

## Prerequisites
- VS Code installed
- DrawX extension project open in VS Code

## Testing Steps

### 1. Launch Extension Development Host

1. Open VS Code with the DrawX project
2. Press `F5` or go to Run → Start Debugging
3. This will open a new VS Code window titled "[Extension Development Host]"
4. The extension is now loaded in this development window

### 2. Test Image Loading

#### Method 1: Command Palette
1. In the Extension Development Host window, press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "DrawX: Select Image from File System"
3. Select the `test-image.svg` file from the project directory
4. The DrawX panel should open in the sidebar with the image loaded

#### Method 2: Explorer Context Menu
1. Open the Explorer panel in the Extension Development Host
2. Right-click on `test-image.svg`
3. Select "Open Image in DrawX" from the context menu
4. The image should load in the DrawX canvas

### 3. Test Drawing Tools

#### Select Tool
- Click the "Select" button (should be active by default)
- Cursor should change to default pointer

#### Draw Tool
1. Click the "Draw" button
2. Cursor should change to crosshair
3. Click and drag on the canvas to draw freehand lines
4. Lines should appear in red (default color)

#### Text Tool
1. Click the "Text" button
2. Cursor should change to text cursor
3. Click anywhere on the canvas
4. A text input box should appear
5. Type some text and press Enter or click outside
6. Text should be rendered on the canvas

#### Highlight Tool
1. Click the "Highlight" button
2. Draw on the canvas
3. Should create semi-transparent yellow highlights

#### Arrow Tool
1. Click the "Arrow" button
2. Click and drag to create arrows
3. Arrows should have proper arrowheads

#### Resize Tool
1. Click the "Resize" button
2. Use mouse wheel while hovering over canvas
3. Image should scale up/down

### 4. Test Keyboard Shortcuts

#### Undo
1. Make some drawings on the canvas
2. Press `Ctrl+Z` (or `Cmd+Z` on Mac)
3. Last action should be undone

### 5. Test Save/Export Functions

#### Save
1. Make some modifications to the image
2. Click the "Save" button
3. Should show success message
4. Original file should be updated

#### Export
1. Make some modifications to the image
2. Click the "Export" button
3. File dialog should open
4. Choose a location and filename
5. New file should be created with modifications

## Expected Results

### ✅ Success Indicators
- Extension loads without errors
- DrawX panel appears in sidebar
- Image loads and displays correctly
- All drawing tools work as expected
- Canvas responds to mouse interactions
- Keyboard shortcuts function properly
- Save/Export operations complete successfully

### ❌ Common Issues to Check
- Extension fails to activate
- Webview doesn't load
- Image doesn't display
- Drawing tools don't respond
- Canvas is blank or shows errors
- Save/Export functions fail

## Debugging

### Check Developer Console
1. In Extension Development Host, press `Ctrl+Shift+I` (or `Cmd+Option+I` on Mac)
2. Look for any JavaScript errors in the Console tab
3. Check Network tab for failed resource loads

### Check Extension Host Console
1. In the main VS Code window (not Extension Development Host)
2. Go to View → Output
3. Select "Extension Host" from the dropdown
4. Look for any extension-related errors

### Common Fixes
- If webview is blank: Check Content Security Policy in HTML
- If images don't load: Verify webview URI conversion
- If tools don't work: Check JavaScript event listeners
- If save fails: Verify file system permissions

## Test Scenarios

### Basic Functionality
- [ ] Extension activates successfully
- [ ] Can load images via command palette
- [ ] Can load images via context menu
- [ ] DrawX panel appears and shows content

### Drawing Tools
- [ ] Select tool works
- [ ] Draw tool creates lines
- [ ] Text tool adds text
- [ ] Highlight tool creates highlights
- [ ] Arrow tool draws arrows
- [ ] Resize tool scales image

### File Operations
- [ ] Save overwrites original file
- [ ] Export creates new file
- [ ] Undo functionality works

### Edge Cases
- [ ] Large images load properly
- [ ] Multiple images can be opened
- [ ] Extension handles unsupported file types gracefully
- [ ] Canvas performance is acceptable

## Performance Testing
- Test with various image sizes (small, medium, large)
- Test with different image formats
- Monitor memory usage during extended use
- Check responsiveness of drawing operations
