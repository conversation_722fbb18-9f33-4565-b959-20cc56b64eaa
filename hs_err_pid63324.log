#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGILL (0x4) at pc=0x00007ff817c31943, pid=63324, tid=259
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, bsd-amd64)
# Problematic frame:
# C  [CoreFoundation+0x1d2943]  CFRunLoopRunSpecific.cold.1+0xe
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/lombok/lombok-1.18.39-4050.jar /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/server/plugins/org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration /Users/<USER>/Library/Application Support/Code - Insiders/User/globalStorage/redhat.java/1.44.0/config_ss_mac -data /Users/<USER>/Library/Application Support/Code - Insiders/User/workspaceStorage/014736ebb37af03afbff696132f9e692/redhat.java/ss_ws --pipe=/private/var/folders/tv/gj9qt8111hx4bkd7w249hb6r0000gn/T/lsp-382b1b3e9d9c664d7f25070799b61582.sock

Host: "iMac13,2" x86_64 3200 MHz, 4 cores, 24G, Darwin 24.3.0, macOS 15.3.1 (24D70)
Time: Wed Aug 20 20:12:57 2025 SAST elapsed time: 108.449323 seconds (0d 0h 1m 48s)

---------------  T H R E A D  ---------------

Current thread is native thread

Stack: [0x00007ff7bdf35000,0x00007ff7be735000],  sp=0x00007ff7be5d4808,  free space=6782k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [CoreFoundation+0x1d2943]  CFRunLoopRunSpecific.cold.1+0xe
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [eclipse_11913.so+0x6d7b]  +[KeyWindow dispatch]+0x7b
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7c480]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7c422]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7c1f9]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7ae1c]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x7a42c]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [eclipse_11913.so+0x6d7b]  +[KeyWindow dispatch]+0x7b
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7c480]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7c422]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7c1f9]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7ae1c]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x7a42c]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [eclipse_11913.so+0x6d7b]  +[KeyWindow dispatch]+0x7b
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7c480]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7c422]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7c1f9]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7ae1c]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x7a42c]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [eclipse_11913.so+0x6d7b]  +[KeyWindow dispatch]+0x7b
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7c480]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7c422]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7c1f9]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7ae1c]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x7a42c]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [eclipse_11913.so+0x6d7b]  +[KeyWindow dispatch]+0x7b
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7c480]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7c422]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7c1f9]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7ae1c]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x7a42c]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [eclipse_11913.so+0x6d7b]  +[KeyWindow dispatch]+0x7b
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7c480]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7c422]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7c1f9]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7ae1c]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x7a42c]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [eclipse_11913.so+0x6d7b]  +[KeyWindow dispatch]+0x7b
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7c480]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7c422]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7c1f9]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7ae1c]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x7a42c]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
C  [AppKit+0x40b33]  _DPSNextEvent+0x386
C  [AppKit+0xa958b8]  -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]+0x50a
C  [eclipse_11913.so+0x6d7b]  +[KeyWindow dispatch]+0x7b
C  [Foundation+0x7c1a9]  __NSThreadPerformPerform+0xb2
C  [CoreFoundation+0x7c480]  __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__+0x11
C  [CoreFoundation+0x7c422]  __CFRunLoopDoSource0+0x9d
C  [CoreFoundation+0x7c1f9]  __CFRunLoopDoSources0+0xcb
C  [CoreFoundation+0x7ae1c]  __CFRunLoopRun+0x3c0
C  [CoreFoundation+0x7a42c]  CFRunLoopRunSpecific+0x226
C  [HIToolbox+0xef413]  RunCurrentEventLoopInMode+0x124
C  [HIToolbox+0xf4c2e]  ReceiveNextEventCommon+0xc0
C  [HIToolbox+0xf5029]  _BlockUntilNextEventMatchingListInModeWithFilter+0x42
...<more frames>...

siginfo: si_signo: 4 (SIGILL), si_code: 1 (ILL_ILLOPC), si_addr: 0x00007ff817c31943

Registers:
RAX=0x00007ff817dc59b2, RBX=0x00007f8b873040c0, RCX=0x00000000000120a8, RDX=0x0000000000000008
RSP=0x00007ff7be5d4808, RBP=0x00007ff7be5d4890, RSI=0x0000000000000000, RDI=0x00007f8b87304418
R8 =0x00000000000fdd1a, R9 =0x0000000000000008, R10=0x0000000000000000, R11=0x00007f8b873043a0
R12=0x0000000000000001, R13=0x00007f8b87304400, R14=0x0000000000000001, R15=0x00007f8b873043f0
RIP=0x00007ff817c31943, EFLAGS=0x0000000000010246, ERR=0x0000000000000000
  TRAPNO=0x0000000000000006


Top of Stack: (sp=0x00007ff7be5d4808)
0x00007ff7be5d4808:   00007ff817ad937d 0000000000000000
0x00007ff7be5d4818:   0000000000000000 0000000000000002
0x00007ff7be5d4828:   00007f8b873040d0 00007ff7be5d4850
0x00007ff7be5d4838:   00007ff817a64499 00007ff8577ec4a8
0x00007ff7be5d4848:   0000000000000001 00007ff7be5d4860
0x00007ff7be5d4858:   00007ff817a9f328 48c00241ece70090
0x00007ff7be5d4868:   00007f8b89718570 0000000000000000
0x00007ff7be5d4878:   0000000000000000 0000000000000001
0x00007ff7be5d4888:   00007ff8577ec4a8 00007ff7be5d48e0
0x00007ff7be5d4898:   00007ff8236d7413 0000000000000000
0x00007ff7be5d48a8:   0000000000000000 0000000000000000
0x00007ff7be5d48b8:   0000000000000000 0000000000000001
0x00007ff7be5d48c8:   0000000000000000 0000000000000000
0x00007ff7be5d48d8:   0000000000000000 00007ff7be5d4960
0x00007ff7be5d48e8:   00007ff8236dcc2e 0000000000000000
0x00007ff7be5d48f8:   00007ff7be5d4ae0 00007ff8577ec4a8
0x00007ff7be5d4908:   0000000000000000 0000000000000000
0x00007ff7be5d4918:   00007ff7be5d4b38 0000000000000000
0x00007ff7be5d4928:   0000000000000000 ffffffffffffffff
0x00007ff7be5d4938:   0000000000000000 00007f8b870db4f0
0x00007ff7be5d4948:   ffffffffffffffff 0000000000000000
0x00007ff7be5d4958:   00007f8b870db4f0 00007ff7be5d4980
0x00007ff7be5d4968:   00007ff8236dd029 ffffffff00000000
0x00007ff7be5d4978:   00007f8b870db4f0 00007ff7be5d4d80
0x00007ff7be5d4988:   00007ff81b3a5b33 0000000000000000
0x00007ff7be5d4998:   0000000000000000 0000000000000000
0x00007ff7be5d49a8:   0000000000000000 0000000000000000
0x00007ff7be5d49b8:   0000000000000000 0000000000000000
0x00007ff7be5d49c8:   0000000000000000 0000000000000000
0x00007ff7be5d49d8:   0000000000000000 0000000000000000
0x00007ff7be5d49e8:   00007ff836d8095c 00007ff836d47ed8
0x00007ff7be5d49f8:   00007ff836e5df26 00007ff836cef8d1 

Instructions: (pc=0x00007ff817c31943)
0x00007ff817c31843:   0f 0b 48 8d 05 24 27 19 00 48 89 05 55 01 d1 42
0x00007ff817c31853:   0f 0b 48 8d 05 14 27 19 00 48 89 05 45 01 d1 42
0x00007ff817c31863:   0f 0b 48 8d 05 04 27 19 00 48 89 05 35 01 d1 42
0x00007ff817c31873:   0f 0b 48 8d 05 f4 26 19 00 48 89 05 25 01 d1 42
0x00007ff817c31883:   0f 0b 48 8d 05 e4 26 19 00 48 89 05 15 01 d1 42
0x00007ff817c31893:   0f 0b 48 8d 05 d4 26 19 00 48 89 05 05 01 d1 42
0x00007ff817c318a3:   0f 0b 48 8d 05 c4 26 19 00 48 89 05 f5 00 d1 42
0x00007ff817c318b3:   0f 0b 66 2e 0f 1f 84 00 00 00 00 00 90 90 90 90
0x00007ff817c318c3:   90 90 48 8d 05 39 3e 19 00 48 89 05 d5 00 d1 42
0x00007ff817c318d3:   0f 0b 48 8d 05 d1 39 19 00 48 89 05 c5 00 d1 42
0x00007ff817c318e3:   0f 0b 48 8d 05 07 40 19 00 48 89 05 b5 00 d1 42
0x00007ff817c318f3:   0f 0b 48 8d 05 5d 40 19 00 48 89 05 a5 00 d1 42
0x00007ff817c31903:   0f 0b 48 8d 05 71 40 19 00 48 89 05 95 00 d1 42
0x00007ff817c31913:   0f 0b 48 8d 05 0a 40 19 00 48 89 05 85 00 d1 42
0x00007ff817c31923:   0f 0b 48 8d 05 fa 3f 19 00 48 89 05 75 00 d1 42
0x00007ff817c31933:   0f 0b 48 8d 05 76 40 19 00 48 89 05 65 00 d1 42
0x00007ff817c31943:   0f 0b 48 8d 05 db 41 19 00 48 89 05 55 00 d1 42
0x00007ff817c31953:   0f 0b 48 8d 05 39 3a 19 00 48 89 05 45 00 d1 42
0x00007ff817c31963:   0f 0b 48 8d 05 b0 3b 19 00 48 89 05 35 00 d1 42
0x00007ff817c31973:   0f 0b 48 8d 05 ff 3b 19 00 48 89 05 25 00 d1 42
0x00007ff817c31983:   0f 0b 55 48 89 e5 48 83 ec 10 48 8b 05 cc 71 97
0x00007ff817c31993:   3f 48 8b 00 48 89 45 f8 48 89 fe 4c 8d 45 f0 66
0x00007ff817c319a3:   41 83 20 00 48 8d 3d 52 d6 e2 ff 48 8d 0d eb 3b
0x00007ff817c319b3:   18 00 6a 10 5a 6a 02 41 59 e8 c9 74 00 00 48 8b
0x00007ff817c319c3:   05 98 71 97 3f 48 8b 00 48 3b 45 f8 75 06 48 83
0x00007ff817c319d3:   c4 10 5d c3 e8 e2 73 00 00 55 48 89 e5 89 f8 f7
0x00007ff817c319e3:   d0 a9 0b 40 00 10 74 1c 89 f8 25 0c 40 00 10 3d
0x00007ff817c319f3:   0c 40 00 10 74 0e e8 ee 78 00 00 48 89 05 a3 ff
0x00007ff817c31a03:   d0 42 0f 0b 81 e7 00 3e 00 00 8d 87 00 fc ff ff
0x00007ff817c31a13:   0f ac f8 0a 83 f8 07 77 e9 48 8d 0d 2d 00 00 00
0x00007ff817c31a23:   48 63 04 81 48 01 c8 ff e0 48 8d 05 60 40 19 00
0x00007ff817c31a33:   eb c9 48 8d 05 31 40 19 00 eb c0 48 8d 05 10 40 



---------------  P R O C E S S  ---------------

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000780000000, size: 2048 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x00000002e3000000-0x00000002e3bb6000-0x00000002e3bb6000), size 12279808, SharedBaseAddress: 0x00000002e3000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000002e4000000-0x0000000324000000, reserved size: 1073741824
Narrow klass base: 0x00000002e3000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
<Skipped>

Heap:
 PSYoungGen      total 29696K, used 24501K [0x00000007d5580000, 0x00000007d7680000, 0x0000000800000000)
  eden space 25600K, 79% used [0x00000007d5580000,0x00000007d6971cf0,0x00000007d6e80000)
  from space 4096K, 99% used [0x00000007d6e80000,0x00000007d727bab0,0x00000007d7280000)
  to   space 4096K, 0% used [0x00000007d7280000,0x00000007d7280000,0x00000007d7680000)
 ParOldGen       total 68608K, used 3734K [0x0000000780000000, 0x0000000784300000, 0x00000007d5580000)
  object space 68608K, 5% used [0x0000000780000000,0x00000007803a5838,0x0000000784300000)
 Metaspace       used 12427K, committed 12800K, reserved 1114112K
  class space    used 1297K, committed 1472K, reserved 1048576K

Card table byte_map: [0x00000001dab1e000,0x00000001daf1f000] _byte_map_base: 0x00000001d6f1e000

Marking Bits: (ParMarkBitMap*) 0x00000002cc134b50
 Begin Bits: [0x00000002de77e000, 0x00000002e077e000)
 End Bits:   [0x00000002e077e000, 0x00000002e277e000)

Polling page: 0x00000001017f3000

Metaspace:

Usage:
  Non-class:     10.87 MB used.
      Class:      1.27 MB used.
       Both:     12.14 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      11.06 MB ( 17%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      12.50 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  4.16 MB
       Class:  14.59 MB
        Both:  18.75 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 238.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 200.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 577.
num_chunk_merges: 0.
num_chunk_splits: 388.
num_chunks_enlarged: 267.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120032Kb used=1103Kb max_used=1103Kb free=118928Kb
 bounds [0x00000002d7246000, 0x00000002d74b6000, 0x00000002de77e000]
CodeHeap 'profiled nmethods': size=120028Kb used=5609Kb max_used=5609Kb free=114418Kb
 bounds [0x00000002cf77e000, 0x00000002cfcfe000, 0x00000002d6cb5000]
CodeHeap 'non-nmethods': size=5700Kb used=1226Kb max_used=1245Kb free=4473Kb
 bounds [0x00000002d6cb5000, 0x00000002d6f25000, 0x00000002d7246000]
CodeCache: size=245760Kb, used=7938Kb, max_used=7957Kb, free=237819Kb
 total_blobs=3306, nmethods=2806, adapters=407, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 106.285 Thread 0x00007f8b86824c00 nmethod 2788 0x00000002cfceaf90 code [0x00000002cfceb140, 0x00000002cfceb2a0]
Event: 106.285 Thread 0x00007f8b86824c00 2786       3       com.sun.org.apache.xerces.internal.util.NamespaceSupport::popContext (21 bytes)
Event: 106.285 Thread 0x00007f8b86824c00 nmethod 2786 0x00000002cfceb390 code [0x00000002cfceb520, 0x00000002cfceb660]
Event: 106.285 Thread 0x00007f8b86824c00 2795       3       com.sun.org.apache.xerces.internal.util.XMLAttributesImpl$Attribute::<init> (27 bytes)
Event: 106.285 Thread 0x00007f8b86824c00 nmethod 2795 0x00000002cfceb710 code [0x00000002cfceb900, 0x00000002cfcebf90]
Event: 106.285 Thread 0x00007f8b86824c00 2796       3       com.sun.org.apache.xerces.internal.impl.XMLEntityScanner::invokeListeners (37 bytes)
Event: 106.285 Thread 0x00007f8b88012200 nmethod 2785 0x00000002d7357a10 code [0x00000002d7357c00, 0x00000002d73580e8]
Event: 106.286 Thread 0x00007f8b88012200 2790       4       java.lang.String::indexOf (64 bytes)
Event: 106.286 Thread 0x00007f8b86824c00 nmethod 2796 0x00000002cfcec210 code [0x00000002cfcec400, 0x00000002cfcec960]
Event: 106.286 Thread 0x00007f8b86824c00 2794       1       com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl::setDriver (6 bytes)
Event: 106.286 Thread 0x00007f8b86824c00 nmethod 2794 0x00000002d7358510 code [0x00000002d73586a0, 0x00000002d7358780]
Event: 106.286 Thread 0x00007f8b86824c00 2787       1       org.eclipse.core.internal.registry.RegistryIndexElement::getKey (5 bytes)
Event: 106.286 Thread 0x00007f8b86824c00 nmethod 2787 0x00000002d7358810 code [0x00000002d73589a0, 0x00000002d7358a68]
Event: 106.287 Thread 0x00007f8b88012200 nmethod 2790 0x00000002d7358b10 code [0x00000002d7358ca0, 0x00000002d7358ef0]
Event: 106.698 Thread 0x00007f8b86824c00 2797   !   3       org.eclipse.osgi.internal.log.ExtendedLogReaderServiceFactory::safeIsLoggable (30 bytes)
Event: 106.699 Thread 0x00007f8b86824c00 nmethod 2797 0x00000002cfcecc10 code [0x00000002cfcece40, 0x00000002cfced548]
Event: 106.847 Thread 0x00007f8b86824c00 2798   !   3       org.eclipse.osgi.internal.loader.BundleLoader::findLocalClass (166 bytes)
Event: 106.849 Thread 0x00007f8b86824c00 nmethod 2798 0x00000002cfced810 code [0x00000002cfcedcc0, 0x00000002cfcefce0]
Event: 106.849 Thread 0x00007f8b86824c00 2799       3       org.eclipse.osgi.internal.loader.ModuleClassLoader::findLocalClass (9 bytes)
Event: 106.849 Thread 0x00007f8b86824c00 nmethod 2799 0x00000002cfcf0a90 code [0x00000002cfcf0c40, 0x00000002cfcf0db0]

GC Heap History (6 events):
Event: 45.695 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000007d5580000, 0x00000007d7680000, 0x0000000800000000)
  eden space 25600K, 100% used [0x00000007d5580000,0x00000007d6e80000,0x00000007d6e80000)
  from space 4096K, 0% used [0x00000007d7280000,0x00000007d7280000,0x00000007d7680000)
  to   space 4096K, 0% used [0x00000007d6e80000,0x00000007d6e80000,0x00000007d7280000)
 ParOldGen       total 68608K, used 1048K [0x0000000780000000, 0x0000000784300000, 0x00000007d5580000)
  object space 68608K, 1% used [0x0000000780000000,0x0000000780106030,0x0000000784300000)
 Metaspace       used 4537K, committed 4736K, reserved 1114112K
  class space    used 500K, committed 576K, reserved 1048576K
}
Event: 45.861 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 2959K [0x00000007d5580000, 0x00000007d7680000, 0x0000000800000000)
  eden space 25600K, 0% used [0x00000007d5580000,0x00000007d5580000,0x00000007d6e80000)
  from space 4096K, 72% used [0x00000007d6e80000,0x00000007d7163e68,0x00000007d7280000)
  to   space 4096K, 0% used [0x00000007d7280000,0x00000007d7280000,0x00000007d7680000)
 ParOldGen       total 68608K, used 1048K [0x0000000780000000, 0x0000000784300000, 0x00000007d5580000)
  object space 68608K, 1% used [0x0000000780000000,0x0000000780106030,0x0000000784300000)
 Metaspace       used 4537K, committed 4736K, reserved 1114112K
  class space    used 500K, committed 576K, reserved 1048576K
}
Event: 65.753 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28559K [0x00000007d5580000, 0x00000007d7680000, 0x0000000800000000)
  eden space 25600K, 100% used [0x00000007d5580000,0x00000007d6e80000,0x00000007d6e80000)
  from space 4096K, 72% used [0x00000007d6e80000,0x00000007d7163e68,0x00000007d7280000)
  to   space 4096K, 0% used [0x00000007d7280000,0x00000007d7280000,0x00000007d7680000)
 ParOldGen       total 68608K, used 1048K [0x0000000780000000, 0x0000000784300000, 0x00000007d5580000)
  object space 68608K, 1% used [0x0000000780000000,0x0000000780106030,0x0000000784300000)
 Metaspace       used 8183K, committed 8512K, reserved 1114112K
  class space    used 883K, committed 1024K, reserved 1048576K
}
Event: 65.761 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4078K [0x00000007d5580000, 0x00000007d7680000, 0x0000000800000000)
  eden space 25600K, 0% used [0x00000007d5580000,0x00000007d5580000,0x00000007d6e80000)
  from space 4096K, 99% used [0x00000007d7280000,0x00000007d767b8a0,0x00000007d7680000)
  to   space 4096K, 0% used [0x00000007d6e80000,0x00000007d6e80000,0x00000007d7280000)
 ParOldGen       total 68608K, used 1288K [0x0000000780000000, 0x0000000784300000, 0x00000007d5580000)
  object space 68608K, 1% used [0x0000000780000000,0x0000000780142030,0x0000000784300000)
 Metaspace       used 8183K, committed 8512K, reserved 1114112K
  class space    used 883K, committed 1024K, reserved 1048576K
}
Event: 87.885 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29678K [0x00000007d5580000, 0x00000007d7680000, 0x0000000800000000)
  eden space 25600K, 100% used [0x00000007d5580000,0x00000007d6e80000,0x00000007d6e80000)
  from space 4096K, 99% used [0x00000007d7280000,0x00000007d767b8a0,0x00000007d7680000)
  to   space 4096K, 0% used [0x00000007d6e80000,0x00000007d6e80000,0x00000007d7280000)
 ParOldGen       total 68608K, used 1288K [0x0000000780000000, 0x0000000784300000, 0x00000007d5580000)
  object space 68608K, 1% used [0x0000000780000000,0x0000000780142030,0x0000000784300000)
 Metaspace       used 9371K, committed 9728K, reserved 1114112K
  class space    used 1000K, committed 1152K, reserved 1048576K
}
Event: 87.889 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4078K [0x00000007d5580000, 0x00000007d7680000, 0x0000000800000000)
  eden space 25600K, 0% used [0x00000007d5580000,0x00000007d5580000,0x00000007d6e80000)
  from space 4096K, 99% used [0x00000007d6e80000,0x00000007d727bab0,0x00000007d7280000)
  to   space 4096K, 0% used [0x00000007d7280000,0x00000007d7280000,0x00000007d7680000)
 ParOldGen       total 68608K, used 3734K [0x0000000780000000, 0x0000000784300000, 0x00000007d5580000)
  object space 68608K, 5% used [0x0000000780000000,0x00000007803a5838,0x0000000784300000)
 Metaspace       used 9371K, committed 9728K, reserved 1114112K
  class space    used 1000K, committed 1152K, reserved 1048576K
}

Dll operation events (9 events):
Event: 1.742 Loaded shared library /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libjava.dylib
Event: 17.802 Loaded shared library /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libzip.dylib
Event: 19.147 Loaded shared library /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libinstrument.dylib
Event: 19.509 Loaded shared library /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libnio.dylib
Event: 19.513 Loaded shared library /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libzip.dylib
Event: 21.557 Loaded shared library /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libjimage.dylib
Event: 27.303 Loaded shared library /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libverify.dylib
Event: 35.580 Loaded shared library /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libnet.dylib
Event: 80.333 Loaded shared library /Users/<USER>/Library/Application Support/Code - Insiders/User/globalStorage/redhat.java/1.44.0/config_ss_mac/org.eclipse.equinox.launcher/org.eclipse.equinox.launcher.cocoa.macosx.x86_64_1.2.1400.v20250730-1736/eclipse_11913.so

Deoptimization events (20 events):
Event: 101.566 Thread 0x00007f8b8787e600 DEOPT PACKING pc=0x00000002cfc7806a sp=0x00007000087ff380
Event: 101.566 Thread 0x00007f8b8787e600 DEOPT UNPACKING pc=0x00000002d6d08b2f sp=0x00007000087fe860 mode 0
Event: 101.567 Thread 0x00007f8b8787e600 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000002d7350d28 relative=0x0000000000000088
Event: 101.567 Thread 0x00007f8b8787e600 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000002d7350d28 method=com.sun.org.apache.xerces.internal.impl.XMLEntityScanner.checkBeforeLoad(Lcom/sun/xml/internal/stream/Entity$ScannedEntity;II)I @ 18 c2
Event: 101.568 Thread 0x00007f8b8787e600 DEOPT PACKING pc=0x00000002d7350d28 sp=0x00007000087ff2f0
Event: 101.568 Thread 0x00007f8b8787e600 DEOPT UNPACKING pc=0x00000002d6d08399 sp=0x00007000087ff280 mode 2
Event: 101.568 Thread 0x00007f8b8787e600 DEOPT PACKING pc=0x00000002cfc7806a sp=0x00007000087ff1a0
Event: 101.568 Thread 0x00007f8b8787e600 DEOPT UNPACKING pc=0x00000002d6d08b2f sp=0x00007000087fe680 mode 0
Event: 106.278 Thread 0x00007f8b8787e600 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000002d72a71c0 relative=0x0000000000001900
Event: 106.278 Thread 0x00007f8b8787e600 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000002d72a71c0 method=java.util.jar.Manifest$FastInputStream.readLine([BII)I @ 106 c2
Event: 106.278 Thread 0x00007f8b8787e600 DEOPT PACKING pc=0x00000002d72a71c0 sp=0x00007000087fec50
Event: 106.278 Thread 0x00007f8b8787e600 DEOPT UNPACKING pc=0x00000002d6d08399 sp=0x00007000087feb30 mode 2
Event: 106.278 Thread 0x00007f8b8787e600 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000002d729cdd8 relative=0x0000000000000478
Event: 106.278 Thread 0x00007f8b8787e600 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000002d729cdd8 method=java.util.jar.Manifest$FastInputStream.readLine([BII)I @ 106 c2
Event: 106.278 Thread 0x00007f8b8787e600 DEOPT PACKING pc=0x00000002d729cdd8 sp=0x00007000087feb80
Event: 106.278 Thread 0x00007f8b8787e600 DEOPT UNPACKING pc=0x00000002d6d08399 sp=0x00007000087feb30 mode 2
Event: 106.278 Thread 0x00007f8b8787e600 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000002d72acfdc relative=0x00000000000000bc
Event: 106.278 Thread 0x00007f8b8787e600 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000002d72acfdc method=java.util.jar.Manifest$FastInputStream.peek()B @ 23 c2
Event: 106.278 Thread 0x00007f8b8787e600 DEOPT PACKING pc=0x00000002d72acfdc sp=0x00007000087fec30
Event: 106.278 Thread 0x00007f8b8787e600 DEOPT UNPACKING pc=0x00000002d6d08399 sp=0x00007000087febe8 mode 2

Classes loaded (20 events):
Event: 94.499 Loading class java/util/concurrent/locks/ReentrantLock$FairSync
Event: 94.623 Loading class java/util/concurrent/locks/ReentrantLock$FairSync done
Event: 97.877 Loading class java/util/PropertyResourceBundle
Event: 97.877 Loading class java/util/PropertyResourceBundle done
Event: 97.877 Loading class sun/util/PropertyResourceBundleCharset
Event: 97.877 Loading class sun/util/PropertyResourceBundleCharset done
Event: 97.877 Loading class sun/util/PropertyResourceBundleCharset$PropertiesFileDecoder
Event: 97.877 Loading class sun/util/PropertyResourceBundleCharset$PropertiesFileDecoder done
Event: 97.881 Loading class java/lang/invoke/DirectMethodHandle$StaticAccessor
Event: 97.881 Loading class java/lang/invoke/DirectMethodHandle$StaticAccessor done
Event: 98.086 Loading class java/lang/IllegalCallerException
Event: 98.086 Loading class java/lang/IllegalCallerException done
Event: 98.137 Loading class java/lang/invoke/DirectMethodHandle$1
Event: 98.207 Loading class java/lang/invoke/DirectMethodHandle$1 done
Event: 98.260 Loading class org/xml/sax/SAXParseException
Event: 98.260 Loading class org/xml/sax/SAXParseException done
Event: 99.747 Loading class java/nio/file/StandardCopyOption
Event: 99.747 Loading class java/nio/file/StandardCopyOption done
Event: 100.666 Loading class sun/nio/fs/UnixFileSystem$Flags
Event: 100.666 Loading class sun/nio/fs/UnixFileSystem$Flags done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 35.575 Thread 0x00007f8b86809400 Exception <a 'sun/nio/fs/UnixException'{0x00000007d657c628}> (0x00000007d657c628) 
thrown [src/hotspot/share/prims/jni.cpp, line 520]
Event: 39.417 Thread 0x00007f8b86809400 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d69f8880}: 'java.lang.ClassLoader java.lang.ClassLoader.getPlatformClassLoader(java.lang.Class)'> (0x00000007d69f8880) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 44.675 Thread 0x00007f8b86809400 Exception <a 'java/io/FileNotFoundException'{0x00000007d6e50858}> (0x00000007d6e50858) 
thrown [src/hotspot/share/prims/jni.cpp, line 520]
Event: 47.147 Thread 0x00007f8b86809400 Implicit null exception at 0x00000002d7298189 to 0x00000002d7298a28
Event: 47.147 Thread 0x00007f8b86809400 Implicit null exception at 0x00000002d72ac89d to 0x00000002d72acaac
Event: 47.147 Thread 0x00007f8b86809400 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d56c2d80}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007d56c2d80) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 47.149 Thread 0x00007f8b86809400 Implicit null exception at 0x00000002d729ee9d to 0x00000002d729f0b4
Event: 47.149 Thread 0x00007f8b86809400 Implicit null exception at 0x00000002d72aa73d to 0x00000002d72aa7c1
Event: 54.287 Thread 0x00007f8b86809400 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d5b08680}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000007d5b08680) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 60.944 Thread 0x00007f8b86809400 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d66b20c0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007d66b20c0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 60.945 Thread 0x00007f8b86809400 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000007d66ba548}: Found class java.lang.Object, but interface was expected> (0x00000007d66ba548) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 840]
Event: 61.060 Thread 0x00007f8b86809400 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d66c0818}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000007d66c0818) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 61.064 Thread 0x00007f8b86809400 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d66c9840}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000007d66c9840) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 62.174 Thread 0x00007f8b86809400 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d68b6d88}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object)'> (0x00000007d68b6d88) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 63.061 Thread 0x00007f8b86809400 Exception <a 'java/lang/ClassNotFoundException'{0x00000007d6a36218}: sun/net/www/protocol/plurl/Handler> (0x00000007d6a36218) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 312]
Event: 63.064 Thread 0x00007f8b86809400 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d6a75be0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007d6a75be0) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 79.134 Thread 0x00007f8b86809400 Exception <a 'java/lang/UnsatisfiedLinkError'{0x00000007d6113958}: 'void org.eclipse.equinox.launcher.JNIBridge._update_splash()'> (0x00000007d6113958) 
thrown [src/hotspot/share/prims/nativeLookup.cpp, line 415]
Event: 87.676 Thread 0x00007f8b8787e600 Exception <a 'java/lang/NullPointerException'{0x00000007d6e3c778}> (0x00000007d6e3c778) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 1372]
Event: 87.676 Thread 0x00007f8b8787e600 Exception <a 'java/lang/NullPointerException'{0x00000007d6e3ca58}> (0x00000007d6e3ca58) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 1372]
Event: 98.032 Thread 0x00007f8b8787e600 Exception <a 'java/lang/NoSuchMethodError'{0x00000007d6330088}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007d6330088) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 92.309 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 92.309 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 92.314 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 92.314 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 93.315 Executing VM operation: Cleanup
Event: 93.315 Executing VM operation: Cleanup done
Event: 94.624 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 94.624 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 95.626 Executing VM operation: Cleanup
Event: 95.626 Executing VM operation: Cleanup done
Event: 97.634 Executing VM operation: Cleanup
Event: 97.634 Executing VM operation: Cleanup done
Event: 97.878 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 97.878 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 98.882 Executing VM operation: Cleanup
Event: 98.883 Executing VM operation: Cleanup done
Event: 99.886 Executing VM operation: Cleanup
Event: 99.886 Executing VM operation: Cleanup done
Event: 101.895 Executing VM operation: Cleanup
Event: 101.896 Executing VM operation: Cleanup done

Memory protections (20 events):
Event: 60.278 Protecting memory [0x00007000083fd000,0x0000700008401000] with protection modes 0
Event: 61.123 Protecting memory [0x0000700008500000,0x0000700008504000] with protection modes 0
Event: 63.639 Protecting memory [0x0000700008603000,0x0000700008607000] with protection modes 0
Event: 63.641 Protecting memory [0x0000700008706000,0x000070000870a000] with protection modes 0
Event: 70.166 Protecting memory [0x00007000080f4000,0x00007000080f8000] with protection modes 3
Event: 70.166 Protecting memory [0x00007000081f7000,0x00007000081fb000] with protection modes 3
Event: 70.278 Protecting memory [0x00007000082fa000,0x00007000082fe000] with protection modes 3
Event: 70.282 Protecting memory [0x00007000083fd000,0x0000700008401000] with protection modes 3
Event: 79.136 Protecting memory [0x00007000080f4000,0x00007000080f8000] with protection modes 0
Event: 79.267 Protecting memory [0x00007000081f7000,0x00007000081fb000] with protection modes 0
Event: 79.267 Protecting memory [0x00007000083fd000,0x0000700008401000] with protection modes 0
Event: 79.268 Protecting memory [0x00007000082fa000,0x00007000082fe000] with protection modes 0
Event: 79.268 Protecting memory [0x0000700008809000,0x000070000880d000] with protection modes 0
Event: 84.896 Protecting memory [0x000070000890c000,0x0000700008910000] with protection modes 0
Event: 89.683 Protecting memory [0x00007000081f7000,0x00007000081fb000] with protection modes 3
Event: 89.683 Protecting memory [0x00007000083fd000,0x0000700008401000] with protection modes 3
Event: 89.683 Protecting memory [0x00007000082fa000,0x00007000082fe000] with protection modes 3
Event: 89.683 Protecting memory [0x0000700008809000,0x000070000880d000] with protection modes 3
Event: 93.644 Protecting memory [0x00007000081f7000,0x00007000081fb000] with protection modes 0
Event: 99.721 Protecting memory [0x00007000081f7000,0x00007000081fb000] with protection modes 3

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 60.278 Thread 0x00007f8b86809400 Thread added: 0x00007f8b88115200
Event: 61.123 Thread 0x00007f8b86809400 Thread added: 0x00007f8b88116800
Event: 63.639 Thread 0x00007f8b86809400 Thread added: 0x00007f8b8787de00
Event: 63.641 Thread 0x00007f8b86809400 Thread added: 0x00007f8b8787e600
Event: 70.166 Thread 0x00007f8b8795d600 Thread exited: 0x00007f8b8795d600
Event: 70.166 Thread 0x00007f8b8787c600 Thread exited: 0x00007f8b8787c600
Event: 70.278 Thread 0x00007f8b868af800 Thread exited: 0x00007f8b868af800
Event: 70.282 Thread 0x00007f8b88115200 Thread exited: 0x00007f8b88115200
Event: 79.131 Thread 0x00007f8b86809400 Thread added: 0x00007f8b868af800
Event: 79.267 Thread 0x00007f8b868af800 Thread added: 0x00007f8b8786e800
Event: 79.267 Thread 0x00007f8b868af800 Thread added: 0x00007f8b87900200
Event: 79.267 Thread 0x00007f8b8786e800 Thread added: 0x00007f8b878ea200
Event: 79.268 Thread 0x00007f8b868af800 Thread added: 0x00007f8b899bc200
Event: 84.896 Thread 0x00007f8b8787e600 Thread added: 0x00007f8b868dfe00
Event: 89.683 Thread 0x00007f8b8786e800 Thread exited: 0x00007f8b8786e800
Event: 89.683 Thread 0x00007f8b878ea200 Thread exited: 0x00007f8b878ea200
Event: 89.683 Thread 0x00007f8b87900200 Thread exited: 0x00007f8b87900200
Event: 89.683 Thread 0x00007f8b899bc200 Thread exited: 0x00007f8b899bc200
Event: 93.644 Thread 0x00007f8b8787e600 Thread added: 0x00007f8b878d2000
Event: 99.721 Thread 0x00007f8b878d2000 Thread exited: 0x00007f8b878d2000


Dynamic libraries:
0x0000000101801000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libjli.dylib
0x00007ff8349af000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x00007ff81a850000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00007ff831fda000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x00007ff8260fe000 	/usr/lib/libSystem.B.dylib
0x00007ff817a5f000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x00007ff818a20000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x00007ff81761c000 	/usr/lib/libobjc.A.dylib
0x00007ff81b365000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x00007ff81e891000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x00007ff81c860000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x00007ffb27592000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00007ff82d80b000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x00007ff8238d8000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00007ff828cd8000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x00007ff828d2f000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x00007ffc30599000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00007ffb1e730000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x00007ffc3505e000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x00007ffc341c1000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x00007ff8186ae000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x00007ff82800f000 	/usr/lib/libspindump.dylib
0x00007ff81ca3b000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x00007ff82540a000 	/usr/lib/libbsm.0.dylib
0x00007ff82151f000 	/usr/lib/libapp_launch_measurement.dylib
0x00007ff8201f4000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00007ff821522000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x0000000101829000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00007ff824191000 	/usr/lib/liblangid.dylib
0x00007ff8238dd000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x00007ff81d45e000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x00007ff81d92d000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00007ff82def1000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00007ff827e45000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x00007ff822f56000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x00007ff820223000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x00007ff826007000 	/usr/lib/libz.1.dylib
0x00007ff8238c5000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x00007ff81ac1f000 	/usr/lib/libicucore.A.dylib
0x00007ff829d8a000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00007ff828ce8000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00007ff92537f000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x00007ff81d3b3000 	/usr/lib/libMobileGestalt.dylib
0x00007ff8235e8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x00007ff820e43000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x00007ff82d842000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x00007ff821233000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x00007ff81a105000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x00007ff820308000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00007ff82845c000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00007ff81d3b2000 	/usr/lib/libenergytrace.dylib
0x00007ff919dfd000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x00007ff81b234000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00007ff82dc46000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x00007ff8214b6000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00007ffa36f67000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x00007ff82156a000 	/usr/lib/libxml2.2.dylib
0x00007ff8252fd000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x00007ff81791d000 	/usr/lib/libc++.1.dylib
0x00007ff82dbc0000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x00007ff81e208000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x00000001017e3000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x00007ff819ee1000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00007ffb1959d000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00007ffb19a50000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x00007ff823915000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00007ffb1f297000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00007ff826103000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00007ff9373d4000 	/usr/lib/swift/libswiftAccelerate.dylib
0x00007ff8297bb000 	/usr/lib/swift/libswiftCore.dylib
0x00007ff92196f000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00007ff9219bf000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00007ff91f322000 	/usr/lib/swift/libswiftDarwin.dylib
0x00007ff82f9c1000 	/usr/lib/swift/libswiftDispatch.dylib
0x00007ff9219c0000 	/usr/lib/swift/libswiftIOKit.dylib
0x00007ff92e475000 	/usr/lib/swift/libswiftMetal.dylib
0x00007ffa1c822000 	/usr/lib/swift/libswiftOSLog.dylib
0x00007ff83246f000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00007ffd1caf6000 	/usr/lib/swift/libswiftObservation.dylib
0x00007ff932e6b000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00007ff9373c5000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00007ff921980000 	/usr/lib/swift/libswiftXPC.dylib
0x00007ffd1cc05000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x00007ffd1cc08000 	/usr/lib/swift/libswift_Concurrency.dylib
0x00007ffd1cd4e000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x00007ffd1cde9000 	/usr/lib/swift/libswift_errno.dylib
0x00007ffd1cdeb000 	/usr/lib/swift/libswift_math.dylib
0x00007ffd1cdee000 	/usr/lib/swift/libswift_signal.dylib
0x00007ffd1cdef000 	/usr/lib/swift/libswift_stdio.dylib
0x00007ffd1cdf0000 	/usr/lib/swift/libswift_time.dylib
0x00007ff832473000 	/usr/lib/swift/libswiftos.dylib
0x00007ff9252d5000 	/usr/lib/swift/libswiftsimd.dylib
0x00007ffd1cdf1000 	/usr/lib/swift/libswiftsys_time.dylib
0x00007ffd1cdf2000 	/usr/lib/swift/libswiftunistd.dylib
0x00007ff826297000 	/usr/lib/libcompression.dylib
0x00007ff828c14000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x00007ff827a35000 	/usr/lib/libate.dylib
0x00007ff8260f8000 	/usr/lib/system/libcache.dylib
0x00007ff8260b0000 	/usr/lib/system/libcommonCrypto.dylib
0x00007ff8260da000 	/usr/lib/system/libcompiler_rt.dylib
0x00007ff8260ce000 	/usr/lib/system/libcopyfile.dylib
0x00007ff817751000 	/usr/lib/system/libcorecrypto.dylib
0x00007ff817849000 	/usr/lib/system/libdispatch.dylib
0x00007ff8179fa000 	/usr/lib/system/libdyld.dylib
0x00007ff8260ee000 	/usr/lib/system/libkeymgr.dylib
0x00007ff826093000 	/usr/lib/system/libmacho.dylib
0x00007ff8253e5000 	/usr/lib/system/libquarantine.dylib
0x00007ff8260eb000 	/usr/lib/system/libremovefile.dylib
0x00007ff81d427000 	/usr/lib/system/libsystem_asl.dylib
0x00007ff8176eb000 	/usr/lib/system/libsystem_blocks.dylib
0x00007ff817894000 	/usr/lib/system/libsystem_c.dylib
0x00007ff8260e2000 	/usr/lib/system/libsystem_collections.dylib
0x00007ff824180000 	/usr/lib/system/libsystem_configuration.dylib
0x00007ff822f29000 	/usr/lib/system/libsystem_containermanager.dylib
0x00007ff825c63000 	/usr/lib/system/libsystem_coreservices.dylib
0x00007ff81aebe000 	/usr/lib/system/libsystem_darwin.dylib
0x00007ffd1cf57000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x00007ff8260ef000 	/usr/lib/system/libsystem_dnssd.dylib
0x00007ffd1cf5b000 	/usr/lib/system/libsystem_eligibility.dylib
0x00007ff817891000 	/usr/lib/system/libsystem_featureflags.dylib
0x00007ff817a31000 	/usr/lib/system/libsystem_info.dylib
0x00007ff82601b000 	/usr/lib/system/libsystem_m.dylib
0x00007ff817805000 	/usr/lib/system/libsystem_malloc.dylib
0x00007ff81d398000 	/usr/lib/system/libsystem_networkextension.dylib
0x00007ff81b2fa000 	/usr/lib/system/libsystem_notify.dylib
0x00007ff824184000 	/usr/lib/system/libsystem_sandbox.dylib
0x00007ffd1cf62000 	/usr/lib/system/libsystem_sanitizers.dylib
0x00007ff8260e7000 	/usr/lib/system/libsystem_secinit.dylib
0x00007ff8179ee000 	/usr/lib/system/libsystem_pthread.dylib
0x00007ff81f158000 	/usr/lib/system/libsystem_symptoms.dylib
0x00007ff817736000 	/usr/lib/system/libsystem_trace.dylib
0x00007ff8260bd000 	/usr/lib/system/libunwind.dylib
0x00007ff8176ef000 	/usr/lib/system/libxpc.dylib
0x00007ff817a27000 	/usr/lib/system/libsystem_platform.dylib
0x00007ff8179b2000 	/usr/lib/system/libsystem_kernel.dylib
0x00007ff81799a000 	/usr/lib/libc++abi.dylib
0x00007ff8260c6000 	/usr/lib/liboah.dylib
0x00007ff81b1b3000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x00007ff826100000 	/usr/lib/libfakelink.dylib
0x00007ff831abc000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00007ff91ee63000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x00007ff81cff4000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x00007ff8214e9000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x00007ff81aec9000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00007ff820281000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x00007ff825c6a000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x00007ff8261dc000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x00007ff81f0d8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x00007ff817efc000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x00007ff827772000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x00007ff8214f7000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x00007ff826261000 	/usr/lib/libapple_nghttp2.dylib
0x00007ff81ed3f000 	/usr/lib/libsqlite3.dylib
0x00007ff82a3d5000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00007ff81f074000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00007ff81f160000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x00007ff826097000 	/usr/lib/system/libkxld.dylib
0x00007ffd1be2d000 	/usr/lib/libCoreEntitlements.dylib
0x00007ffc1c1ae000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x00007ff81ed26000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x00007ff825c49000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x00007ff8253f2000 	/usr/lib/libcoretls.dylib
0x00007ff8277df000 	/usr/lib/libcoretls_cfhelpers.dylib
0x00007ff826291000 	/usr/lib/libpam.2.dylib
0x00007ff827859000 	/usr/lib/libxar.1.dylib
0x00007ff826153000 	/usr/lib/libarchive.2.dylib
0x00007ff82baf6000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x00007ffb275b9000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x00007ffc2344e000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x00007ffc23fbe000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x00007ffd1cb8d000 	/usr/lib/swift/libswiftSystem.dylib
0x00007ff82418c000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x0000000101873000 	/System/Library/PrivateFrameworks/CoreWiFi.framework/Versions/A/CoreWiFi
0x00007ff92186c000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00007ff8216db000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x00007ff81cf21000 	/usr/lib/libboringssl.dylib
0x00007ff81f148000 	/usr/lib/libdns_services.dylib
0x00007ff920abe000 	/usr/lib/libquic.dylib
0x00007ff829743000 	/usr/lib/libusrtcp.dylib
0x00007ffa24144000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x00007ffd1cac6000 	/usr/lib/swift/libswiftDistributed.dylib
0x00007ff81cff3000 	/usr/lib/libnetwork.dylib
0x00007ff82623f000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x0000000101ab9000 	/System/Library/PrivateFrameworks/CoreWiFi.framework/Versions/A/CoreWiFiOld.dylib
0x0000000101845000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00007ff82fc34000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x0000000101859000 	/System/Library/PrivateFrameworks/WiFiPeerToPeer.framework/Versions/A/WiFiPeerToPeer
0x00007ff825324000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000016aed2000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211Old.dylib
0x00007ff81ef91000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00007ff82fd05000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x00007ff825317000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00007ff8318f5000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x00007ff823367000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x00007ff81d43e000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00007ff835363000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x00007ff82845a000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x00007ff819a03000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x00007ff825107000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00007ff823216000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x00007ff822f6d000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x00007ff82164a000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x00007ff82628f000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x00007ffc22a3b000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x00007ff8284a0000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x00007ff82418b000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00007ff8277e1000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x00007ffb24240000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x00007ff827867000 	/usr/lib/libutil.dylib
0x00007ff8277c0000 	/usr/lib/liblzma.5.dylib
0x00007ff820310000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00007ff82dc1d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x00007ff82789c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x00007ff81836f000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x00007ff829fbf000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x00007ff81e3be000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x00007ff828ba0000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x00007ff82a39e000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x00007ff82a397000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x00007ff829f95000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x00007ff8250d5000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00007ffb2dd75000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x00007ff827fb3000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x00007ff8211e7000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x00007ff828003000 	/System/Library/PrivateFrameworks/FontServices.framework/libhvf.dylib
0x00007ffc1f218000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x00007ffd1cb11000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x00007ffd1cca6000 	/usr/lib/swift/libswift_RegexParser.dylib
0x00007ff828323000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x00007ff827dd5000 	/usr/lib/libexpat.1.dylib
0x00007ff828a6b000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x00007ff828a98000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x00007ff828b8b000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00007ff82836f000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x00007ff828b2b000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x00007ff828b22000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x00000001daf4a000 	/System/Library/Frameworks/Metal.framework/Versions/A/MetalOld.dylib
0x00007ffa36f5b000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x00007ffb2dd76000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00007ffb2de3b000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00007ffa1fed9000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00007ffc21dc5000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x00000001dba14000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImageOld.dylib
0x00007ffb17f25000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x00007ff827fa4000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00007ffa36fb8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00007ffa36f7a000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00007ffa37174000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00007ffa36f83000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00007ffa36f77000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00007ffd1bf5c000 	/usr/lib/libRosetta.dylib
0x00007ffa36f62000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00007ff8240c5000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x00007ff825ba3000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x00007ff82548f000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x00007ff8259e8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x00007ff82579f000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x00007ff825a27000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00007ffb1b9d6000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00007ffb1b9b9000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x00007ff8181e8000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00007ff9267e1000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00007ff933271000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00007ff92194e000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x00007ff83015e000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00007ff9218ee000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x00007ff827b6c000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x00007ff83011f000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x00007ff9324ec000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x00007ff828b5c000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x00007ff82a329000 	/usr/lib/libcups.2.dylib
0x00007ff82a3c6000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x00007ff82a038000 	/usr/lib/libresolv.9.dylib
0x00007ff82613a000 	/usr/lib/libiconv.2.dylib
0x00007ff828016000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00007ff8323cb000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x00007ff827df0000 	/usr/lib/libheimdal-asn1.dylib
0x00007ff8214c1000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x00007ff82a423000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x00007ff8214cd000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x00007ff826092000 	/usr/lib/libcharset.1.dylib
0x00007ffb1867c000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00007ff91bca1000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x00007ffc19289000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x00007ff829f7d000 	/usr/lib/libAudioStatistics.dylib
0x00007ff8231f2000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x00007ff819b2e000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00007ff8347bd000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x00007ff829f24000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00007ff82b882000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x00007ff827eda000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x00007ffc2e9e2000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x00007ffa17a4f000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00007ff827ff8000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x00007ff82a3a9000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00007ff920ba8000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x00007ff82a249000 	/usr/lib/libSMC.dylib
0x00007ff828a36000 	/usr/lib/libAudioToolboxUtility.dylib
0x00007ff82a3b7000 	/usr/lib/libperfcheck.dylib
0x00007ffb2483d000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00007ffa2ac43000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x00007ffa23f1f000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00007ff825c54000 	/usr/lib/libbz2.1.0.dylib
0x00007ff92191d000 	/usr/lib/libmis.dylib
0x00007ff829d4a000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x00007ff828b91000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00007ffb19b71000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00007ff83026e000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x00007ff827cc6000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00007ff82eb85000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00007ff82ea95000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00007ff8323cc000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x00007ff825192000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x00007ffb2b0ca000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00007ff82df3d000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00007ff929a52000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x00007ffd1bc6a000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00007ff91bd46000 	/usr/lib/libAccessibility.dylib
0x00007ff824ede000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x00007ff82637a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x00007ff825479000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x00007ff82627c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x00007ff826374000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00007ff8242a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x00007ff818637000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x00007ffc1a48a000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x00007ff828b1b000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x00007ff828aff000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x00007ff828b25000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00007ffa2433a000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00007ff91aa7d000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x00007ffc31f6a000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x00007ff827d87000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00007ff82fdad000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x00000001dbfcc000 	/System/Library/PrivateFrameworks/WiFiPeerToPeer.framework/Versions/A/WiFiPeerToPeerOld.dylib
0x00007ff825e6e000 	/System/Library/Frameworks/UserNotifications.framework/Versions/A/UserNotifications
0x00007ff82a58f000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00007ff82ec65000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00007ff82ebbf000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x00007ff82a38c000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x00007ff82169e000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x00007ff827df9000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x00007ff827b63000 	/usr/lib/libIOReport.dylib
0x00007ffb2447e000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x00007ffd1bfa1000 	/usr/lib/libTLE.dylib
0x00007ffa3279e000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x00007ff82541a000 	/usr/lib/libmecab.dylib
0x00007ff818734000 	/usr/lib/libCRFSuite.dylib
0x00007ff81853e000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x00007ff8242a2000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00007ff824193000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x00007ff825476000 	/usr/lib/libgermantok.dylib
0x00007ff826237000 	/usr/lib/libThaiTokenizer.dylib
0x00007ff8253e8000 	/usr/lib/libCheckFix.dylib
0x00007ff820225000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x00007ffb367d3000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x00007ff81b1f4000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00007ff933373000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00007ff82786b000 	/usr/lib/libxslt.1.dylib
0x00007ff8253ae000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00007ff831c6a000 	/usr/lib/libcurl.4.dylib
0x00007ffd1c335000 	/usr/lib/libcrypto.46.dylib
0x00007ffd1c8a6000 	/usr/lib/libssl.48.dylib
0x00007ff83194e000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00007ff831989000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x00007ff82a051000 	/usr/lib/libsasl2.2.dylib
0x00007ff91f321000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00007ff82e54e000 	/usr/lib/swift/libswiftFoundation.dylib
0x00007ffa32255000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00007ffa205c3000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x00007ffc2409f000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x00007ff82d832000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x00000002cb2c1000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/server/libjvm.dylib
0x00000001018f2000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libjimage.dylib
0x0000000101915000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libinstrument.dylib
0x0000000101953000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libjava.dylib
0x00000001db735000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libzip.dylib
0x00000001db75d000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libnio.dylib
0x00000001db70d000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libnet.dylib
0x00000001db779000 	/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/jre/21.0.8-macosx-x86_64/lib/libverify.dylib
0x00000001db7ae000 	/Users/<USER>/Library/Application Support/Code - Insiders/User/globalStorage/redhat.java/1.44.0/config_ss_mac/org.eclipse.equinox.launcher/org.eclipse.equinox.launcher.cocoa.macosx.x86_64_1.2.1400.v20250730-1736/eclipse_11913.so
0x00007ff82139f000 	/System/Library/PrivateFrameworks/ViewBridge.framework/Versions/A/ViewBridge

JVMTI agents:
/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/lombok/lombok-1.18.39-4050.jar path:none, loaded, initialized, instrumentlib options:none


VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:/Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/lombok/lombok-1.18.39-4050.jar 
java_command: /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/server/plugins/org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration /Users/<USER>/Library/Application Support/Code - Insiders/User/globalStorage/redhat.java/1.44.0/config_ss_mac -data /Users/<USER>/Library/Application Support/Code - Insiders/User/workspaceStorage/014736ebb37af03afbff696132f9e692/redhat.java/ss_ws --pipe=/private/var/folders/tv/gj9qt8111hx4bkd7w249hb6r0000gn/T/lsp-382b1b3e9d9c664d7f25070799b61582.sock
java_class_path (initial): /Users/<USER>/.vscode-insiders/extensions/redhat.java-1.44.0-darwin-x64/server/plugins/org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin
SHELL=/bin/zsh
TMPDIR=/var/folders/tv/gj9qt8111hx4bkd7w249hb6r0000gn/T/

Active Locale:
LC_ALL=C
LC_COLLATE=C
LC_CTYPE=C
LC_MESSAGES=C
LC_MONETARY=C
LC_NUMERIC=C
LC_TIME=C

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO, unblocked
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked


Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.3.0 Darwin Kernel Version 24.3.0: Thu Jan  2 20:22:00 PST 2025; root:xnu-11215.81.4~3/RELEASE_X86_64 x86_64
OS uptime: 0 days 16:34 hours
rlimit (soft/hard): STACK 8192k/65532k , CORE 0k/infinity , NPROC 4176/6264 , NOFILE 1048576/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 3.60 3.01 3.06

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 58 stepping 9 microcode 0x21, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, avx, aes, erms, clmul, vzeroupper, clflush, rdtscp, f16c
machdep.cpu.brand_string:Intel(R) Core(TM) i5-3470 CPU @ 3.20GHz
hw.cpufrequency:3200000000
hw.cpufrequency_min:3200000000
hw.cpufrequency_max:3200000000
hw.cachelinesize:64
hw.l1icachesize:32768
hw.l1dcachesize:32768
hw.l2cachesize:262144
hw.l3cachesize:6291456

Memory: 4k page, physical 25165824k(37348k free), swap 0k(0k free)

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for bsd-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with clang Apple LLVM 15.0.0 (clang-1500.1.0.2.5)

END.
