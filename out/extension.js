"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const DrawXWebviewProvider_1 = require("./webview/DrawXWebviewProvider");
let drawXProvider;
function activate(context) {
    console.log('DrawX extension is now active!');
    // Initialize the webview provider
    drawXProvider = new DrawXWebviewProvider_1.DrawXWebviewProvider(context.extensionUri);
    // Register webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider(DrawXWebviewProvider_1.DrawXWebviewProvider.viewType, drawXProvider));
    // Register command to open image from explorer context
    const openImageCommand = vscode.commands.registerCommand('drawx.openImage', async (uri) => {
        if (uri && uri.fsPath) {
            await drawXProvider.loadImage(uri.fsPath);
            // Set context to show the webview
            vscode.commands.executeCommand('setContext', 'drawx.hasActiveImage', true);
            // Focus on the DrawX view
            vscode.commands.executeCommand('workbench.view.extension.drawx');
        }
    });
    // Register command to select image from file system
    const selectImageCommand = vscode.commands.registerCommand('drawx.selectImage', async () => {
        const options = {
            canSelectMany: false,
            openLabel: 'Select Image',
            filters: {
                'Images': ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg']
            }
        };
        const fileUri = await vscode.window.showOpenDialog(options);
        if (fileUri && fileUri[0]) {
            await drawXProvider.loadImage(fileUri[0].fsPath);
            // Set context to show the webview
            vscode.commands.executeCommand('setContext', 'drawx.hasActiveImage', true);
            // Focus on the DrawX view
            vscode.commands.executeCommand('workbench.view.extension.drawx');
        }
    });
    context.subscriptions.push(openImageCommand);
    context.subscriptions.push(selectImageCommand);
}
exports.activate = activate;
function deactivate() {
    console.log('DrawX extension is now deactivated!');
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map