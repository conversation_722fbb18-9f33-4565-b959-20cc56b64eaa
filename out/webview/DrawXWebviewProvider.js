"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DrawXWebviewProvider = void 0;
const vscode = require("vscode");
const path = require("path");
const fs = require("fs");
class DrawXWebviewProvider {
    constructor(_extensionUri) {
        this._extensionUri = _extensionUri;
    }
    resolveWebviewView(webviewView, context, _token) {
        this._view = webviewView;
        webviewView.webview.options = {
            // Allow scripts in the webview
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri,
                vscode.Uri.file(path.dirname(this._currentImagePath || ''))
            ]
        };
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(message => {
            switch (message.type) {
                case 'saveImage':
                    this._saveImage(message.imageData);
                    break;
                case 'exportImage':
                    this._exportImage(message.imageData);
                    break;
            }
        }, undefined, []);
    }
    async loadImage(imagePath) {
        this._currentImagePath = imagePath;
        if (this._view) {
            // Update webview options to include the image directory
            this._view.webview.options = {
                enableScripts: true,
                localResourceRoots: [
                    this._extensionUri,
                    vscode.Uri.file(path.dirname(imagePath))
                ]
            };
            // Convert the image path to a webview URI
            const imageUri = this._view.webview.asWebviewUri(vscode.Uri.file(imagePath));
            // Send the image to the webview
            this._view.webview.postMessage({
                type: 'loadImage',
                imageUri: imageUri.toString(),
                imagePath: imagePath
            });
            // Show the webview
            this._view.show?.(true);
        }
    }
    async _saveImage(imageData) {
        if (!this._currentImagePath) {
            vscode.window.showErrorMessage('No image loaded to save');
            return;
        }
        try {
            // Remove data URL prefix
            const base64Data = imageData.replace(/^data:image\/\w+;base64,/, '');
            const buffer = Buffer.from(base64Data, 'base64');
            // Save to the original file location
            fs.writeFileSync(this._currentImagePath, buffer);
            vscode.window.showInformationMessage('Image saved successfully!');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to save image: ${error}`);
        }
    }
    async _exportImage(imageData) {
        const options = {
            defaultUri: vscode.Uri.file(path.join(vscode.workspace.rootPath || '', 'exported-image.png')),
            filters: {
                'PNG Images': ['png'],
                'JPEG Images': ['jpg', 'jpeg']
            }
        };
        const fileUri = await vscode.window.showSaveDialog(options);
        if (fileUri) {
            try {
                // Remove data URL prefix
                const base64Data = imageData.replace(/^data:image\/\w+;base64,/, '');
                const buffer = Buffer.from(base64Data, 'base64');
                fs.writeFileSync(fileUri.fsPath, buffer);
                vscode.window.showInformationMessage('Image exported successfully!');
            }
            catch (error) {
                vscode.window.showErrorMessage(`Failed to export image: ${error}`);
            }
        }
    }
    _getHtmlForWebview(webview) {
        // Get the local path to main script run in the webview, then convert it to a uri we can use in the webview.
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'canvas.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'canvas.css'));
        // Use a nonce to only allow a specific script to be run.
        const nonce = getNonce();
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}'; img-src ${webview.cspSource} data:;">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${styleUri}" rel="stylesheet">
                <title>DrawX Canvas</title>
            </head>
            <body>
                <div id="toolbar">
                    <button id="selectTool" class="tool-btn active" data-tool="select">Select</button>
                    <button id="drawTool" class="tool-btn" data-tool="draw">Draw</button>
                    <button id="textTool" class="tool-btn" data-tool="text">Text</button>
                    <button id="highlightTool" class="tool-btn" data-tool="highlight">Highlight</button>
                    <button id="arrowTool" class="tool-btn" data-tool="arrow">Arrow</button>
                    <button id="resizeTool" class="tool-btn" data-tool="resize">Resize</button>
                    <div class="separator"></div>
                    <button id="saveBtn" class="action-btn">Save</button>
                    <button id="exportBtn" class="action-btn">Export</button>
                </div>
                <div id="canvasContainer">
                    <canvas id="drawCanvas"></canvas>
                </div>
                <script nonce="${nonce}" src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
exports.DrawXWebviewProvider = DrawXWebviewProvider;
DrawXWebviewProvider.viewType = 'drawx.canvas';
function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}
//# sourceMappingURL=DrawXWebviewProvider.js.map