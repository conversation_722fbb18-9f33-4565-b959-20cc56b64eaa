{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,yEAAsE;AAEtE,IAAI,aAAmC,CAAC;AAExC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAE9C,kCAAkC;IAClC,aAAa,GAAG,IAAI,2CAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAE/D,4BAA4B;IAC5B,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,2CAAoB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAC1F,CAAC;IAEF,uDAAuD;IACvD,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,KAAK,EAAE,GAAe,EAAE,EAAE;QAClG,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE;YACnB,MAAM,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC1C,kCAAkC;YAClC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,sBAAsB,EAAE,IAAI,CAAC,CAAC;YAC3E,0BAA0B;YAC1B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;SACpE;IACL,CAAC,CAAC,CAAC;IAEH,oDAAoD;IACpD,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,KAAK,IAAI,EAAE;QACvF,MAAM,OAAO,GAA6B;YACtC,aAAa,EAAE,KAAK;YACpB,SAAS,EAAE,cAAc;YACzB,OAAO,EAAE;gBACL,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;aACxD;SACJ,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;YACvB,MAAM,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACjD,kCAAkC;YAClC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,sBAAsB,EAAE,IAAI,CAAC,CAAC;YAC3E,0BAA0B;YAC1B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;SACpE;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC7C,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACnD,CAAC;AA5CD,4BA4CC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;AACvD,CAAC;AAFD,gCAEC"}