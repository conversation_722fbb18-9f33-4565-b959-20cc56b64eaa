{"name": "drawx", "displayName": "DrawX", "description": "Image manipulation and drawing extension for VS Code", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onCommand:drawx.openImage"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "drawx.openImage", "title": "Open Image in DrawX", "category": "DrawX"}, {"command": "drawx.selectImage", "title": "Select Image from File System", "category": "DrawX"}], "menus": {"explorer/context": [{"command": "drawx.openImage", "when": "resourceExtname =~ /\\.(png|jpg|jpeg|gif|bmp|svg)$/i", "group": "navigation"}], "commandPalette": [{"command": "drawx.openImage"}, {"command": "drawx.selectImage"}]}, "viewsContainers": {"activitybar": [{"id": "drawx", "title": "DrawX", "icon": "$(paintbrush)"}]}, "views": {"drawx": [{"type": "webview", "id": "drawx.canvas", "name": "<PERSON><PERSON>", "when": "drawx.hasActiveImage"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {}}