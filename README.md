# DrawX - VS Code Image Drawing Extension

DrawX is a powerful VS Code extension that allows you to manipulate and annotate images directly within your development environment.

## Features

- **Image Loading**: Load images from your file system or open them directly from the VS Code explorer
- **Drawing Tools**: 
  - Freehand drawing with customizable colors and brush sizes
  - Text annotations with custom positioning
  - Highlight tool for emphasizing areas
  - Arrow tool for pointing and directing attention
  - Selection tool for precise editing
- **Image Manipulation**:
  - Resize images using mouse wheel when in resize mode
  - Zoom and pan functionality
  - Undo/Redo support (Ctrl+Z)
- **Save & Export**: Save changes back to the original file or export to a new location

## Usage

### Opening Images

1. **From Explorer**: Right-click on any image file (.png, .jpg, .jpeg, .gif, .bmp, .svg) in the VS Code explorer and select "Open Image in DrawX"
2. **From Command Palette**: 
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "DrawX: Select Image from File System"
   - Choose an image file from the dialog

### Drawing Tools

- **Select Tool**: Default tool for selecting and moving elements
- **Draw Tool**: Freehand drawing with customizable stroke color and width
- **Text Tool**: Click anywhere on the canvas to add text annotations
- **Highlight Tool**: Create semi-transparent highlight overlays
- **Arrow Tool**: Draw arrows by clicking and dragging from start to end point
- **Resize Tool**: Use mouse wheel to resize the entire canvas/image

### Keyboard Shortcuts

- `Ctrl+Z`: Undo last action
- `Delete`: Remove selected elements (when implemented)

### Saving Your Work

- **Save**: Overwrites the original image file with your modifications
- **Export**: Save your modified image to a new location

## Installation

1. Clone this repository
2. Run `npm install` to install dependencies
3. Run `npm run compile` to build the extension
4. Press `F5` to open a new VS Code window with the extension loaded

## Development

### Building

```bash
npm run compile
```

### Watching for Changes

```bash
npm run watch
```

### Packaging

```bash
vsce package
```

## Requirements

- VS Code 1.74.0 or higher
- Node.js for development

## Known Issues

- Large images may impact performance
- Some image formats may not be fully supported in all browsers

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is licensed under the MIT License.
