body {
    margin: 0;
    padding: 0;
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    overflow: hidden;
}

#toolbar {
    display: flex;
    align-items: center;
    padding: 8px;
    background-color: var(--vscode-sideBar-background);
    border-bottom: 1px solid var(--vscode-sideBar-border);
    gap: 4px;
    flex-wrap: wrap;
}

.tool-btn, .action-btn {
    padding: 6px 12px;
    border: 1px solid var(--vscode-button-border);
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    cursor: pointer;
    border-radius: 3px;
    font-size: 12px;
    transition: background-color 0.2s;
}

.tool-btn:hover, .action-btn:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.tool-btn.active {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.separator {
    width: 1px;
    height: 20px;
    background-color: var(--vscode-sideBar-border);
    margin: 0 8px;
}

#canvasContainer {
    position: relative;
    width: 100%;
    height: calc(100vh - 50px);
    overflow: auto;
    background-color: var(--vscode-editor-background);
    background-image: 
        linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

#drawCanvas {
    display: block;
    margin: 20px auto;
    border: 1px solid var(--vscode-panel-border);
    background-color: white;
    cursor: crosshair;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#drawCanvas.select-mode {
    cursor: default;
}

#drawCanvas.text-mode {
    cursor: text;
}

#drawCanvas.resize-mode {
    cursor: nw-resize;
}

/* Color picker and tool options */
.tool-options {
    display: none;
    align-items: center;
    gap: 8px;
    margin-left: 8px;
}

.tool-options.active {
    display: flex;
}

.color-picker {
    width: 30px;
    height: 24px;
    border: 1px solid var(--vscode-button-border);
    border-radius: 3px;
    cursor: pointer;
}

.size-slider {
    width: 80px;
}

/* Text input overlay */
.text-input-overlay {
    position: absolute;
    background: transparent;
    border: 2px dashed var(--vscode-focusBorder);
    padding: 4px;
    font-family: Arial, sans-serif;
    font-size: 16px;
    color: #000;
    outline: none;
    resize: none;
    overflow: hidden;
    z-index: 10;
}

/* Resize handles */
.resize-handle {
    position: absolute;
    background-color: var(--vscode-focusBorder);
    border: 1px solid white;
    width: 8px;
    height: 8px;
    z-index: 20;
}

.resize-handle.nw { top: -4px; left: -4px; cursor: nw-resize; }
.resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
.resize-handle.sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.resize-handle.se { bottom: -4px; right: -4px; cursor: se-resize; }
.resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
.resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
.resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
.resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

/* Loading state */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 14px;
    color: var(--vscode-descriptionForeground);
}

/* No image state */
.no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--vscode-descriptionForeground);
}

.no-image h3 {
    margin-bottom: 8px;
    color: var(--vscode-editor-foreground);
}

.no-image p {
    margin: 4px 0;
    font-size: 12px;
}
