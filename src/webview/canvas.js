(function() {
    const vscode = acquireVsCodeApi();
    
    let canvas, ctx;
    let currentTool = 'select';
    let isDrawing = false;
    let startX, startY;
    let currentImage = null;
    let imageScale = 1;
    let imageOffsetX = 0;
    let imageOffsetY = 0;
    let drawingHistory = [];
    let currentPath = [];
    
    // Drawing settings
    let strokeColor = '#ff0000';
    let strokeWidth = 2;
    let fillColor = '#ffff00';
    
    // Initialize the canvas
    function initCanvas() {
        canvas = document.getElementById('drawCanvas');
        ctx = canvas.getContext('2d');
        
        // Set initial canvas size
        canvas.width = 800;
        canvas.height = 600;
        
        setupEventListeners();
        showNoImageState();
    }
    
    function showNoImageState() {
        const container = document.getElementById('canvasContainer');
        container.innerHTML = `
            <div class="no-image">
                <h3>No Image Loaded</h3>
                <p>Use "Select Image from File System" command to load an image</p>
                <p>Or right-click on an image file in the explorer and select "Open Image in DrawX"</p>
            </div>
        `;
    }
    
    function setupEventListeners() {
        // Tool buttons
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                currentTool = e.target.dataset.tool;
                updateCanvasCursor();
            });
        });

        // Action buttons
        document.getElementById('saveBtn').addEventListener('click', saveImage);
        document.getElementById('exportBtn').addEventListener('click', exportImage);

        // Canvas events
        canvas.addEventListener('mousedown', handleMouseDown);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseup', handleMouseUp);
        canvas.addEventListener('click', handleClick);
        canvas.addEventListener('wheel', handleWheel);

        // Keyboard shortcuts
        document.addEventListener('keydown', handleKeyDown);
    }
    
    function updateCanvasCursor() {
        canvas.className = '';
        switch(currentTool) {
            case 'select':
                canvas.classList.add('select-mode');
                break;
            case 'text':
                canvas.classList.add('text-mode');
                break;
            case 'resize':
                canvas.classList.add('resize-mode');
                break;
            default:
                // Default crosshair for drawing tools
                break;
        }
    }
    
    function handleMouseDown(e) {
        const rect = canvas.getBoundingClientRect();
        startX = e.clientX - rect.left;
        startY = e.clientY - rect.top;
        
        if (currentTool === 'draw' || currentTool === 'highlight') {
            isDrawing = true;
            currentPath = [{x: startX, y: startY}];
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            
            if (currentTool === 'highlight') {
                ctx.globalCompositeOperation = 'multiply';
                ctx.strokeStyle = fillColor;
                ctx.lineWidth = 20;
            } else {
                ctx.globalCompositeOperation = 'source-over';
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = strokeWidth;
            }
        }
    }
    
    function handleMouseMove(e) {
        if (!isDrawing) return;
        
        const rect = canvas.getBoundingClientRect();
        const currentX = e.clientX - rect.left;
        const currentY = e.clientY - rect.top;
        
        if (currentTool === 'draw' || currentTool === 'highlight') {
            currentPath.push({x: currentX, y: currentY});
            
            ctx.lineTo(currentX, currentY);
            ctx.stroke();
        }
    }
    
    function handleMouseUp(e) {
        if (isDrawing) {
            isDrawing = false;
            
            if (currentTool === 'arrow') {
                const rect = canvas.getBoundingClientRect();
                const endX = e.clientX - rect.left;
                const endY = e.clientY - rect.top;
                drawArrow(startX, startY, endX, endY);
            }
            
            // Save the current state to history
            saveToHistory();
        }
    }
    
    function handleClick(e) {
        if (currentTool === 'text') {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            createTextInput(x, y);
        }
    }
    
    function drawArrow(fromX, fromY, toX, toY) {
        const headLength = 15;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        
        ctx.beginPath();
        ctx.strokeStyle = strokeColor;
        ctx.lineWidth = strokeWidth;
        ctx.globalCompositeOperation = 'source-over';
        
        // Draw the line
        ctx.moveTo(fromX, fromY);
        ctx.lineTo(toX, toY);
        
        // Draw the arrowhead
        ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6));
        ctx.moveTo(toX, toY);
        ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6));
        
        ctx.stroke();
    }
    
    function createTextInput(x, y) {
        const textInput = document.createElement('textarea');
        textInput.className = 'text-input-overlay';
        textInput.style.left = x + 'px';
        textInput.style.top = y + 'px';
        textInput.style.width = '200px';
        textInput.style.height = '30px';
        textInput.placeholder = 'Enter text...';
        
        document.getElementById('canvasContainer').appendChild(textInput);
        textInput.focus();
        
        textInput.addEventListener('blur', () => {
            if (textInput.value.trim()) {
                drawText(textInput.value, x, y);
                saveToHistory();
            }
            textInput.remove();
        });
        
        textInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                textInput.blur();
            }
        });
    }
    
    function drawText(text, x, y) {
        ctx.font = '16px Arial';
        ctx.fillStyle = strokeColor;
        ctx.globalCompositeOperation = 'source-over';
        
        const lines = text.split('\n');
        lines.forEach((line, index) => {
            ctx.fillText(line, x, y + (index + 1) * 20);
        });
    }
    
    function saveToHistory() {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        drawingHistory.push(imageData);
        
        // Limit history to prevent memory issues
        if (drawingHistory.length > 20) {
            drawingHistory.shift();
        }
    }
    
    function loadImage(imageUri, imagePath) {
        const img = new Image();
        img.onload = function() {
            currentImage = img;
            
            // Resize canvas to fit image
            canvas.width = img.width;
            canvas.height = img.height;
            
            // Clear canvas and draw image
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
            
            // Save initial state
            drawingHistory = [];
            saveToHistory();
            
            // Show canvas
            const container = document.getElementById('canvasContainer');
            container.innerHTML = '';
            container.appendChild(canvas);
        };
        img.src = imageUri;
    }
    
    function saveImage() {
        if (!currentImage) {
            vscode.postMessage({
                type: 'error',
                message: 'No image to save'
            });
            return;
        }
        
        const imageData = canvas.toDataURL('image/png');
        vscode.postMessage({
            type: 'saveImage',
            imageData: imageData
        });
    }
    
    function exportImage() {
        if (!currentImage) {
            vscode.postMessage({
                type: 'error',
                message: 'No image to export'
            });
            return;
        }

        const imageData = canvas.toDataURL('image/png');
        vscode.postMessage({
            type: 'exportImage',
            imageData: imageData
        });
    }

    function handleWheel(e) {
        if (currentTool === 'resize' && currentImage) {
            e.preventDefault();

            const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
            const newWidth = canvas.width * scaleFactor;
            const newHeight = canvas.height * scaleFactor;

            // Limit scaling
            if (newWidth > 50 && newWidth < 5000 && newHeight > 50 && newHeight < 5000) {
                resizeCanvas(newWidth, newHeight);
            }
        }
    }

    function handleKeyDown(e) {
        // Undo with Ctrl+Z
        if (e.ctrlKey && e.key === 'z') {
            e.preventDefault();
            undo();
        }

        // Delete selected elements with Delete key
        if (e.key === 'Delete') {
            // Implementation for deleting selected elements would go here
        }
    }

    function resizeCanvas(newWidth, newHeight) {
        // Create a temporary canvas to store current content
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        tempCanvas.width = canvas.width;
        tempCanvas.height = canvas.height;
        tempCtx.drawImage(canvas, 0, 0);

        // Resize main canvas
        canvas.width = newWidth;
        canvas.height = newHeight;

        // Redraw content scaled
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(tempCanvas, 0, 0, tempCanvas.width, tempCanvas.height, 0, 0, newWidth, newHeight);

        saveToHistory();
    }

    function undo() {
        if (drawingHistory.length > 1) {
            drawingHistory.pop(); // Remove current state
            const previousState = drawingHistory[drawingHistory.length - 1];
            ctx.putImageData(previousState, 0, 0);
        }
    }
    
    // Handle messages from the extension
    window.addEventListener('message', event => {
        const message = event.data;
        
        switch (message.type) {
            case 'loadImage':
                loadImage(message.imageUri, message.imagePath);
                break;
        }
    });
    
    // Initialize when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCanvas);
    } else {
        initCanvas();
    }
})();
