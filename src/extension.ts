import * as vscode from 'vscode';
import { DrawXWebviewProvider } from './webview/DrawXWebviewProvider';

let drawXProvider: DrawXWebviewProvider;

export function activate(context: vscode.ExtensionContext) {
    console.log('DrawX extension is now active!');

    // Initialize the webview provider
    drawXProvider = new DrawXWebviewProvider(context.extensionUri);

    // Register webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(DrawXWebviewProvider.viewType, drawXProvider)
    );

    // Register command to open image from explorer context
    const openImageCommand = vscode.commands.registerCommand('drawx.openImage', async (uri: vscode.Uri) => {
        if (uri && uri.fsPath) {
            await drawXProvider.loadImage(uri.fsPath);
            // Set context to show the webview
            vscode.commands.executeCommand('setContext', 'drawx.hasActiveImage', true);
            // Focus on the DrawX view
            vscode.commands.executeCommand('workbench.view.extension.drawx');
        }
    });

    // Register command to select image from file system
    const selectImageCommand = vscode.commands.registerCommand('drawx.selectImage', async () => {
        const options: vscode.OpenDialogOptions = {
            canSelectMany: false,
            openLabel: 'Select Image',
            filters: {
                'Images': ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg']
            }
        };

        const fileUri = await vscode.window.showOpenDialog(options);
        if (fileUri && fileUri[0]) {
            await drawXProvider.loadImage(fileUri[0].fsPath);
            // Set context to show the webview
            vscode.commands.executeCommand('setContext', 'drawx.hasActiveImage', true);
            // Focus on the DrawX view
            vscode.commands.executeCommand('workbench.view.extension.drawx');
        }
    });

    context.subscriptions.push(openImageCommand);
    context.subscriptions.push(selectImageCommand);
}

export function deactivate() {
    console.log('DrawX extension is now deactivated!');
}
